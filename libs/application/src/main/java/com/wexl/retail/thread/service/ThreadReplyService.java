package com.wexl.retail.thread.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.model.Thread;
import com.wexl.retail.thread.model.ThreadReply;
import com.wexl.retail.thread.repository.ThreadReplyRepository;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ThreadReplyService {

  private final TeacherService teacherService;
  private final ThreadReplyRepository threadReplyRepository;
  private final ThreadService threadService;
  private final NotificationsService notificationsService;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;
  private final UserRepository userRepository;

  public void createThreadReply(
      ThreadDto.CreateThreadReplyRequest request, String teacherAuthId, Long threadId) {
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    var thread = threadService.findThreadById(threadId);

    var 
    // Save the thread reply
    threadReplyRepository.save(buildThreadRequest(request, thread, teacher));

    // Send notifications to student-side and admin-side
    sendNotifications(request, teacher, thread);
  }

  private ThreadReply buildThreadRequest(
      ThreadDto.CreateThreadReplyRequest request, Thread thread, Teacher teacher) {
    return ThreadReply.builder().reply(request.reply()).teacher(teacher).thread(thread).build();
  }

  private void sendNotifications(
      ThreadDto.CreateThreadReplyRequest request, Teacher teacher, Thread thread) {

    // Get the student who created the thread
    var student = thread.getCreatedBy();

    // Get all organization admins
    List<Teacher> orgAdmins = teacherRepository.getAllAdminsByOrg(thread.getOrgSlug());

    // Collect all auth user IDs to send notifications to
    List<String> authUserIds = new ArrayList<>();

    // Add student's auth user ID for student-side notification
    authUserIds.add(student.getUserInfo().getAuthUserId());

    // Add all admin auth user IDs for admin-side notifications
    authUserIds.addAll(
        orgAdmins.stream().map(Teacher::getUserInfo).map(User::getAuthUserId).toList());

    // Send notifications to all recipients
    for (String authId : authUserIds) {
      var notificationRequest =
          NotificationDto.NotificationRequest.builder()
              .title("New Thread Reply")
              .message(request.reply())
              .notificationType(NotificationType.FORUM)
              .userAuthId(authId)
              .build();

      notificationsService.createNotificationByTeacher(
          thread.getOrgSlug(), notificationRequest, teacher.getUserInfo().getAuthUserId(), false);

      eventNotificationService.sendPushNotificationForUser(
          authId,
          request.reply(),
          thread.getOrgSlug(),
          NotificationType.FORUM,
          "Thread Reply Notification");
    }
  }

  public ThreadReply findThreadReplyById(Long threadReplyId) {
    var threadReply = threadReplyRepository.findById(threadReplyId);
    if (threadReply.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ThreadReplyId",
          new String[] {threadReplyId.toString()});
    }
    return threadReply.get();
  }

  public void editThreadReply(
      Long threadReplyId, String teacherAuthId, ThreadDto.CreateThreadReplyRequest request) {
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    var threadReply =
        threadReplyRepository
            .findByIdAndTeacher(threadReplyId, teacher)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ThreadReplyId"));
    threadReply.setReply(request.reply());
    threadReply.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    threadReplyRepository.save(threadReply);
  }
}
